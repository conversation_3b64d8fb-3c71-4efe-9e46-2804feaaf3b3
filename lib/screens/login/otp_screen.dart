import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import '../../constants/app_colors.dart';
import '../../constants/size.dart';

class OTPScreen extends StatefulWidget {
  const OTPScreen({super.key});

  @override
  State<OTPScreen> createState() => _OTPScreenState();
}

class _OTPScreenState extends State<OTPScreen> {
  final TextEditingController _otpController = TextEditingController();

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Upper Logo Section
            Expanded(
              flex: 1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/brand_logo.png',
                      width: MySize.size60,
                      height: MySize.size60,
                    ),
                    Space.height(12),
                    Image.asset(
                      'assets/images/brand_logo_text.png',
                      width: MySize.size180,
                    ),
                  ],
                ),
              ),
            ),

            // Lower OTP Container
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.size24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: Shape.circularTop(32),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Heading
                    Text(
                      'Verify Your Number',
                      style: TextStyle(
                        fontSize: MySize.size22,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    Space.height(8),

                    // Subtitle
                    Text(
                      "We've sent a 5-digit code to your phone number.",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Space.height(32),

                    // OTP Input (Pinput)
                    Pinput(
                      length: 6,
                      controller: _otpController,
                      defaultPinTheme: PinTheme(
                        width: MySize.size56,
                        height: MySize.size56,
                        textStyle: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: Shape.circular(12),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                      ),
                      focusedPinTheme: PinTheme(
                        width: MySize.size56,
                        height: MySize.size56,
                        textStyle: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: Shape.circular(12),
                          border: Border.all(color: AppColors.primaryColor),
                        ),
                      ),
                    ),
                    Space.height(12),

                    // Resend OTP
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          // Resend logic here
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: Size(MySize.size50, MySize.size30),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Resend Otp',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ),
                    Space.height(16),

                    // Verify Button
                    SizedBox(
                      width: double.infinity,
                      height: MySize.size56,
                      child: ElevatedButton(
                        onPressed: () {
                          // Verify logic here
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          shape: Shape.circular(30, shapeTypeFor: ShapeTypeFor.button),
                        ),
                        child: Text(
                          'Verify',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
