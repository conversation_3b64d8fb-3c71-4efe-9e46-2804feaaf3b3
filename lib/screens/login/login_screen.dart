import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/size.dart';
import '../../widgets/custom_text_field.dart';
import 'otp_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // UPPER HALF
            Expanded(
              flex: 1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/brand_logo.png',
                      width: MySize.size60,
                      height: MySize.size60,
                    ),
                    Space.height(12),
                    Image.asset(
                      'assets/images/brand_logo_text.png',
                      width: MySize.size180,
                    ),
                  ],
                ),
              ),
            ),

            // LOWER HALF
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.size24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: Shape.circularTop(32),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Heading
                    Center(
                      child: Text(
                        'Login/Signup',
                        style: TextStyle(
                          fontSize: MySize.size24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    Space.height(8),

                    // Subtitle
                    Center(
                      child: Text(
                        'Login/Signup to start managing your orders.',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    Space.height(50),

                    // Phone Number label
                    Text(
                      'Phone Number',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    Space.height(8),

                    // Phone number field
                    CustomTextField(
                      controller: _phoneController,
                      hintText: 'Enter Your Phone Number',
                      keyboardType: TextInputType.phone,
                      borderColor: Colors.grey[300],
                      focusedBorderColor: AppColors.primaryColor,
                    ),

                    Space.height(10),

                    // Next button
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: MySize.size40, vertical: MySize.size40),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const OTPScreen(),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            padding: EdgeInsets.symmetric(vertical: MySize.size16),
                            elevation: 0,
                            shape: Shape.circular(30, shapeTypeFor: ShapeTypeFor.button),
                          ),
                          child: Text(
                            'Next',
                            style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.blackColor
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
