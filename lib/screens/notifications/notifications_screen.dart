import 'package:flutter/material.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Notifications',
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Today Section
                    _buildSectionHeader('Today'),
                    Space.height(16),

                    _buildNotificationItem(
                      icon: Icons.inventory_2,
                      iconColor: AppColors.primaryColor,
                      title: 'New Order Received',
                      description: 'Order #0001 has been placed by krishna. 3 items totaling ₹ 450',
                      time: '2 Min ago',
                    ),

                    Space.height(12),

                    _buildNotificationItem(
                      icon: Icons.payment,
                      iconColor: AppColors.primaryColor,
                      title: 'Payment Confirmed',
                      description: 'Payment of ₹ 550 has been successfully Processed for order #0001',
                      time: '15 Min ago',
                    ),

                    Space.height(12),

                    _buildNotificationItem(
                      icon: Icons.local_shipping,
                      iconColor: AppColors.primaryColor,
                      title: 'Order out for Delivery',
                      description: 'Order #0001 is now out for Delivery. Expected Delivery: 2:30 PM',
                      time: '1 Day ago',
                    ),

                    Space.height(32),

                    // Yesterday Section
                    _buildSectionHeader('Yesterday'),
                    Space.height(16),

                    _buildNotificationItem(
                      icon: Icons.check_circle,
                      iconColor: AppColors.primaryColor,
                      title: 'Order Completed',
                      description: 'Order #0001 has been successfully delivered and marked as complete',
                      time: '2 Day ago',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: MySize.size18,
        fontWeight: FontWeight.w600,
        color: AppColors.blackColor,
      ),
    );
  }

  Widget _buildNotificationItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
    required String time,
  }) {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size4,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon Container
          Container(
            width: MySize.size40,
            height: MySize.size40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: MySize.size20,
            ),
          ),

          Space.width(12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: MySize.size14,
                          color: AppColors.primaryColor,
                        ),
                        Space.width(4),
                        Text(
                          time,
                          style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Space.height(4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.greyColor,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
