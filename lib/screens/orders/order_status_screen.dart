import 'package:flutter/material.dart';
import 'package:packagingwala/widgets/custom_app_bar.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';

class OrderStatusScreen extends StatefulWidget {
  final String orderId;
  final String orderDate;
  final String orderImage;
  final String stepTitle;

  const OrderStatusScreen({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.orderImage,
    required this.stepTitle,
  });

  @override
  State<OrderStatusScreen> createState() => _OrderStatusScreenState();
}

class _OrderStatusScreenState extends State<OrderStatusScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              title: widget.stepTitle,
              showBackButton: true,
            ),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Header
                    _buildOrderHeader(),

                    Space.height(24),

                    // Recent Details Section
                    _buildRecentDetails(),

                    Space.height(24),

                    // Remarks Section
                    _buildRemarksSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Shape.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: MySize.size4,
            offset: Offset(0, MySize.size2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: Shape.circular(8),
            child: Image.asset(
              widget.orderImage,
              width: MySize.size60,
              height: MySize.size60,
              fit: BoxFit.cover,
            ),
          ),
          Space.width(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${widget.orderId}',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
                Space.height(4),
                Text(
                  'Placed On ${widget.orderDate}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.greyColor,
                  ),
                ),
                Space.height(8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: Shape.circular(12),
                  ),
                  child: Text(
                    'Delivered',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Details',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),

        // Recent Photo Section
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Large Image
              ClipRRect(
                borderRadius: Shape.circularTop(12),
                child: Image.asset(
                  widget.orderImage,
                  width: double.infinity,
                  height: MySize.size200,
                  fit: BoxFit.cover,
                ),
              ),

              // Photo Details
              Padding(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recent Photo of Your Order',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                    ),
                    Space.height(8),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: MySize.size16,
                          color: AppColors.primaryColor,
                        ),
                        Space.width(4),
                        Text(
                          widget.orderDate,
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.greyColor,
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.access_time,
                          size: MySize.size16,
                          color: AppColors.primaryColor,
                        ),
                        Space.width(4),
                        Text(
                          '09:00 AM',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRemarksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Remarks From Team',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.w600,
            color: AppColors.blackColor,
          ),
        ),
        Space.height(16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: Shape.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: MySize.size4,
                offset: Offset(0, MySize.size2),
              ),
            ],
          ),
          child: Text(
            'We are working on your order on priority basis. You\'ll get an update in next 4 days.',
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.greyColor,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }
}