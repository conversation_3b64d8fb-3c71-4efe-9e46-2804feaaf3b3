import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:packagingwala/constants/app_colors.dart';

/// A smart SVG icon widget that handles problematic SVG files gracefully
/// and provides fallback options for icons with embedded images
class SmartSvgIcon extends StatelessWidget {
  final String assetPath;
  final double? width;
  final double? height;
  final ColorFilter? colorFilter;
  final IconData? fallbackIcon;
  final Color? fallbackColor;

  const SmartSvgIcon({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.colorFilter,
    this.fallbackIcon,
    this.fallbackColor,
  });

  /// List of SVG files that are known to have embedded images and may not render properly
  static const Set<String> _problematicSvgFiles = {
    'assets/icons/approval_icon.svg',
    'assets/icons/curing_icon.svg',
    'assets/icons/cylinder_icon.svg',
    'assets/icons/dispatched_icon.svg',
    'assets/icons/lamination_icon.svg',
    'assets/icons/pasting_icon.svg',
    'assets/icons/polyster_approved_icon.svg',
    'assets/icons/polyster_printing_icon.svg',
    'assets/icons/pouching_icon.svg',
    'assets/icons/sampling_icon.svg',
    'assets/icons/sorting_icon.svg',
    'assets/icons/zipper_icon.svg',
  };

  /// Mapping of problematic SVG files to fallback Material Icons
  static const Map<String, IconData> _fallbackIconMap = {
    'assets/icons/approval_icon.svg': Icons.check_circle,
    'assets/icons/curing_icon.svg': Icons.healing,
    'assets/icons/cylinder_icon.svg': Icons.circle,
    'assets/icons/dispatched_icon.svg': Icons.local_shipping,
    'assets/icons/lamination_icon.svg': Icons.layers,
    'assets/icons/pasting_icon.svg': Icons.content_paste,
    'assets/icons/polyster_approved_icon.svg': Icons.verified,
    'assets/icons/polyster_printing_icon.svg': Icons.print,
    'assets/icons/pouching_icon.svg': Icons.inventory_2,
    'assets/icons/sampling_icon.svg': Icons.science,
    'assets/icons/sorting_icon.svg': Icons.sort,
    'assets/icons/zipper_icon.svg': Icons.compress,
  };

  @override
  Widget build(BuildContext context) {
    // Check if this is a problematic SVG file
    if (_problematicSvgFiles.contains(assetPath)) {
      return _buildFallbackIcon();
    }

    // Try to render the SVG, with fallback on error
    return SvgPicture.asset(
      assetPath,
      width: width,
      height: height,
      colorFilter: colorFilter,
      placeholderBuilder: (context) => _buildFallbackIcon(),
    );
  }

  Widget _buildFallbackIcon() {
    final IconData iconData = fallbackIcon ??
        _fallbackIconMap[assetPath] ??
        Icons.help_outline;

    final Color iconColor = fallbackColor ?? AppColors.blackColor;

    return Icon(
      iconData,
      size: width ?? height ?? 24.0,
      color: iconColor,
    );
  }

  /// Factory constructor for creating icons with specific colors
  factory SmartSvgIcon.colored({
    required String assetPath,
    required Color color,
    double? size,
    IconData? fallbackIcon,
  }) {
    return SmartSvgIcon(
      assetPath: assetPath,
      width: size,
      height: size,
      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
      fallbackIcon: fallbackIcon,
      fallbackColor: color,
    );
  }

  /// Factory constructor for black icons
  factory SmartSvgIcon.black({
    required String assetPath,
    double? size,
    IconData? fallbackIcon,
  }) {
    return SmartSvgIcon.colored(
      assetPath: assetPath,
      color: AppColors.blackColor,
      size: size,
      fallbackIcon: fallbackIcon,
    );
  }

  /// Factory constructor for grey icons
  factory SmartSvgIcon.grey({
    required String assetPath,
    double? size,
    IconData? fallbackIcon,
  }) {
    return SmartSvgIcon.colored(
      assetPath: assetPath,
      color: AppColors.greyColor,
      size: size,
      fallbackIcon: fallbackIcon,
    );
  }

  /// Factory constructor for primary color icons
  factory SmartSvgIcon.primary({
    required String assetPath,
    double? size,
    IconData? fallbackIcon,
  }) {
    return SmartSvgIcon.colored(
      assetPath: assetPath,
      color: AppColors.primaryColor,
      size: size,
      fallbackIcon: fallbackIcon,
    );
  }
}
