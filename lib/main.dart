import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:packagingwala/constants/size.dart';
import 'package:packagingwala/screens/login/animated_auth_screen.dart';
import 'package:packagingwala/screens/home/<USER>';
import 'package:packagingwala/test_svg_icons.dart';
import 'constants/app_colors.dart';

late ProviderContainer globalProviderContainer;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    ProviderScope(
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    globalProviderContainer = ProviderScope.containerOf(context);

    MySize().init(context);
    SizeConfig().init(context);

    return MaterialApp(
      title: 'Packaging Wala',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: AppColors.primaryColor,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primaryColor,
          primary: AppColors.primaryColor,
        ),
        useMaterial3: true,
      ),
      home: const HomeScreen(), // Test with home screen to access order details
    );
  }
}
