import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:packagingwala/constants/app_colors.dart';
import 'package:packagingwala/constants/size.dart';

/// Test screen to verify which SVG icons are working and which are not
class TestSvgIconsScreen extends StatelessWidget {
  const TestSvgIconsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SVG Icons Test'),
        backgroundColor: AppColors.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.size16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Working SVG Icons (Pure Vector):',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            Space.height(16),
            _buildIconGrid([
              'assets/icons/onboarding_icon.svg',
              'assets/icons/designing_icon.svg',
              'assets/icons/heating_icon.svg',
              'assets/icons/packing_icon.svg',
              'assets/icons/readytodispatch_icon.svg',
              'assets/icons/slitting_icon.svg',
              'assets/icons/notification_icon.svg',
              'assets/icons/privacy_policy_icon.svg',
              'assets/icons/support_icon.svg',
            ]),
            
            Space.height(32),
            
            Text(
              'Problematic SVG Icons (Embedded Images):',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            Space.height(16),
            _buildIconGrid([
              'assets/icons/approval_icon.svg',
              'assets/icons/curing_icon.svg',
              'assets/icons/cylinder_icon.svg',
              'assets/icons/dispatched_icon.svg',
              'assets/icons/lamination_icon.svg',
              'assets/icons/pasting_icon.svg',
              'assets/icons/polyster_approved_icon.svg',
              'assets/icons/polyster_printing_icon.svg',
              'assets/icons/pouching_icon.svg',
              'assets/icons/sampling_icon.svg',
              'assets/icons/sorting_icon.svg',
              'assets/icons/zipper_icon.svg',
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildIconGrid(List<String> iconPaths) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: iconPaths.length,
      itemBuilder: (context, index) {
        return _buildIconItem(iconPaths[index]);
      },
    );
  }

  Widget _buildIconItem(String iconPath) {
    final iconName = iconPath.split('/').last.replaceAll('_icon.svg', '');
    
    return Column(
      children: [
        Container(
          width: MySize.size40,
          height: MySize.size40,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: SvgPicture.asset(
              iconPath,
              width: MySize.size20,
              height: MySize.size20,
              colorFilter: ColorFilter.mode(
                AppColors.blackColor,
                BlendMode.srcIn,
              ),
              // Add error handling
              placeholderBuilder: (context) => Icon(
                Icons.error,
                size: MySize.size20,
                color: Colors.red,
              ),
            ),
          ),
        ),
        Space.height(4),
        Text(
          iconName,
          style: TextStyle(
            fontSize: MySize.size10,
            color: AppColors.blackColor,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
